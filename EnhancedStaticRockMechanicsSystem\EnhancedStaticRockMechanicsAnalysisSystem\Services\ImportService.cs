using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using EnhancedStaticRockMechanicsAnalysisSystem.Models;
using NPOI.HSSF.UserModel;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Services
{
    /// <summary>
    /// 数据导入服务
    /// </summary>
    public class ImportService
    {
        /// <summary>
        /// 从CSV文件导入数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的数据点列表</returns>
        public List<RockMechanicsDataPoint> ImportFromCsv(string filePath)
        {
            var dataPoints = new List<RockMechanicsDataPoint>();

            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var lines = File.ReadAllLines(filePath);
                if (lines.Length < 2) // 至少需要标题行和一行数据
                {
                    throw new InvalidDataException("CSV文件格式不正确或没有数据");
                }

                // 跳过标题行
                for (int i = 1; i < lines.Length; i++)
                {
                    var line = lines[i].Trim();
                    if (string.IsNullOrEmpty(line))
                        continue;

                    var dataPoint = ParseCsvLine(line, i + 1);
                    if (dataPoint != null)
                    {
                        dataPoints.Add(dataPoint);
                    }
                }

                LoggingService.Instance.Info($"从CSV文件成功导入 {dataPoints.Count} 条数据: {filePath}");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"CSV文件导入失败: {filePath}", ex);
                throw;
            }

            return dataPoints;
        }

        /// <summary>
        /// 解析CSV行数据
        /// </summary>
        /// <param name="line">CSV行</param>
        /// <param name="lineNumber">行号</param>
        /// <returns>数据点</returns>
        private RockMechanicsDataPoint? ParseCsvLine(string line, int lineNumber)
        {
            try
            {
                var values = line.Split(',');
                if (values.Length < 3)
                {
                    LoggingService.Instance.Warning($"CSV第{lineNumber}行数据不完整，跳过");
                    return null;
                }

                // 解析基本参数：密度、纵波速度、横波速度
                if (!double.TryParse(values[0].Trim(), out double density) ||
                    !double.TryParse(values[1].Trim(), out double vp) ||
                    !double.TryParse(values[2].Trim(), out double vs))
                {
                    LoggingService.Instance.Warning($"CSV第{lineNumber}行数据格式错误，跳过");
                    return null;
                }

                var dataPoint = new RockMechanicsDataPoint(density, vp, vs);

                // 如果有更多列，尝试解析计算结果
                if (values.Length > 3 && double.TryParse(values[3].Trim(), out double youngModulus))
                {
                    dataPoint.YoungModulus = youngModulus;
                }

                if (values.Length > 4 && double.TryParse(values[4].Trim(), out double poissonRatio))
                {
                    dataPoint.PoissonRatio = poissonRatio;
                }

                if (values.Length > 5 && double.TryParse(values[5].Trim(), out double brittlenessIndex))
                {
                    dataPoint.BrittlenessIndex = brittlenessIndex;
                }

                // 验证数据有效性
                if (!dataPoint.IsValid())
                {
                    LoggingService.Instance.Warning($"CSV第{lineNumber}行数据无效，跳过");
                    return null;
                }

                return dataPoint;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Warning($"解析CSV第{lineNumber}行时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从Excel文件导入数据
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>导入的数据点列表</returns>
        public List<RockMechanicsDataPoint> ImportFromExcel(string filePath)
        {
            var dataPoints = new List<RockMechanicsDataPoint>();

            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                using (var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read))
                {
                    IWorkbook workbook;
                    var extension = Path.GetExtension(filePath).ToLower();

                    // 根据文件扩展名选择合适的工作簿类型
                    if (extension == ".xlsx")
                    {
                        workbook = new XSSFWorkbook(fileStream);
                    }
                    else if (extension == ".xls")
                    {
                        workbook = new HSSFWorkbook(fileStream);
                    }
                    else
                    {
                        throw new NotSupportedException($"不支持的Excel文件格式: {extension}");
                    }

                    // 获取第一个工作表
                    var sheet = workbook.GetSheetAt(0);
                    if (sheet == null)
                    {
                        throw new InvalidDataException("Excel文件中没有找到工作表");
                    }

                    // 跳过标题行，从第二行开始读取数据
                    for (int rowIndex = 1; rowIndex <= sheet.LastRowNum; rowIndex++)
                    {
                        var row = sheet.GetRow(rowIndex);
                        if (row == null) continue;

                        var dataPoint = ParseExcelRow(row, rowIndex + 1);
                        if (dataPoint != null)
                        {
                            dataPoints.Add(dataPoint);
                        }
                    }
                }

                LoggingService.Instance.Info($"从Excel文件成功导入 {dataPoints.Count} 条数据: {filePath}");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"Excel文件导入失败: {filePath}", ex);
                throw;
            }

            return dataPoints;
        }

        /// <summary>
        /// 解析Excel行数据
        /// </summary>
        /// <param name="row">Excel行</param>
        /// <param name="lineNumber">行号</param>
        /// <returns>解析后的数据点</returns>
        private RockMechanicsDataPoint? ParseExcelRow(IRow row, int lineNumber)
        {
            try
            {
                // 获取单元格值的辅助方法
                double GetCellValue(int cellIndex)
                {
                    var cell = row.GetCell(cellIndex);
                    if (cell == null) return 0.0;

                    switch (cell.CellType)
                    {
                        case CellType.Numeric:
                            return cell.NumericCellValue;
                        case CellType.String:
                            if (double.TryParse(cell.StringCellValue, out double value))
                                return value;
                            break;
                        case CellType.Formula:
                            try
                            {
                                return cell.NumericCellValue;
                            }
                            catch
                            {
                                if (double.TryParse(cell.ToString(), out double formulaValue))
                                    return formulaValue;
                            }
                            break;
                    }
                    return 0.0;
                }

                // 读取必需的三列数据：密度、纵波速度、横波速度
                var density = GetCellValue(0);      // 第1列：密度
                var vpVelocity = GetCellValue(1);   // 第2列：纵波速度
                var vsVelocity = GetCellValue(2);   // 第3列：横波速度

                // 验证数据有效性
                if (density <= 0 || vpVelocity <= 0 || vsVelocity <= 0)
                {
                    LoggingService.Instance.Warning($"Excel第{lineNumber}行数据无效，跳过处理");
                    return null;
                }

                var dataPoint = new RockMechanicsDataPoint
                {
                    Density = density,
                    VpVelocity = vpVelocity,
                    VsVelocity = vsVelocity
                };

                return dataPoint;
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"解析Excel第{lineNumber}行数据失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 验证导入文件格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public (bool IsValid, string Message) ValidateImportFile(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    return (false, "文件不存在");
                }

                var extension = Path.GetExtension(filePath).ToLower();
                if (extension != ".csv" && extension != ".xlsx" && extension != ".xls")
                {
                    return (false, "不支持的文件格式，请选择CSV或Excel文件");
                }

                var fileInfo = new FileInfo(filePath);
                if (fileInfo.Length == 0)
                {
                    return (false, "文件为空");
                }

                if (fileInfo.Length > 10 * 1024 * 1024) // 10MB限制
                {
                    return (false, "文件过大，请选择小于10MB的文件");
                }

                // 对于CSV文件，检查基本格式
                if (extension == ".csv")
                {
                    var lines = File.ReadLines(filePath).Take(5).ToArray();
                    if (lines.Length < 2)
                    {
                        return (false, "CSV文件至少需要包含标题行和一行数据");
                    }

                    // 检查第一行数据格式
                    if (lines.Length > 1)
                    {
                        var values = lines[1].Split(',');
                        if (values.Length < 3)
                        {
                            return (false, "CSV文件格式不正确，至少需要3列数据（密度、纵波速度、横波速度）");
                        }
                    }
                }

                return (true, "文件格式验证通过");
            }
            catch (Exception ex)
            {
                LoggingService.Instance.Error($"验证导入文件时发生错误: {filePath}", ex);
                return (false, $"文件验证失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取支持的文件格式
        /// </summary>
        /// <returns>文件格式过滤器</returns>
        public string GetSupportedFileFilter()
        {
            return "支持的文件|*.csv;*.xlsx;*.xls|CSV文件|*.csv|Excel文件|*.xlsx;*.xls|所有文件|*.*";
        }

        /// <summary>
        /// 批量导入多个文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>导入结果</returns>
        public (List<RockMechanicsDataPoint> DataPoints, List<string> Errors) ImportMultipleFiles(string[] filePaths)
        {
            var allDataPoints = new List<RockMechanicsDataPoint>();
            var errors = new List<string>();

            foreach (var filePath in filePaths)
            {
                try
                {
                    var extension = Path.GetExtension(filePath).ToLower();
                    List<RockMechanicsDataPoint> dataPoints;

                    if (extension == ".csv")
                    {
                        dataPoints = ImportFromCsv(filePath);
                    }
                    else if (extension == ".xlsx" || extension == ".xls")
                    {
                        dataPoints = ImportFromExcel(filePath);
                    }
                    else
                    {
                        errors.Add($"不支持的文件格式: {filePath}");
                        continue;
                    }

                    allDataPoints.AddRange(dataPoints);
                    LoggingService.Instance.Info($"成功导入文件: {filePath}, 数据点数量: {dataPoints.Count}");
                }
                catch (Exception ex)
                {
                    var errorMessage = $"导入文件失败: {filePath} - {ex.Message}";
                    errors.Add(errorMessage);
                    LoggingService.Instance.Error(errorMessage, ex);
                }
            }

            return (allDataPoints, errors);
        }
    }
}
