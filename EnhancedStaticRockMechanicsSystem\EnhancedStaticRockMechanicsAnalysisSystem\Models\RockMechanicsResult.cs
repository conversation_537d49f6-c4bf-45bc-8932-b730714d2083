using System;

namespace EnhancedStaticRockMechanicsAnalysisSystem.Models
{
    /// <summary>
    /// 岩石力学计算结果
    /// </summary>
    public class RockMechanicsResult
    {
        /// <summary>
        /// 动态杨氏模量 (GPa)
        /// </summary>
        public double Ed { get; set; }

        /// <summary>
        /// 动态泊松比
        /// </summary>
        public double MuD { get; set; }

        /// <summary>
        /// 静态杨氏模量 (GPa)
        /// </summary>
        public double Es { get; set; }

        /// <summary>
        /// 静态泊松比
        /// </summary>
        public double MuS { get; set; }

        /// <summary>
        /// 脆性指数 (%)
        /// </summary>
        public double BrittlenessIndex { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public RockMechanicsResult()
        {
            Ed = 0;
            MuD = 0;
            Es = 0;
            MuS = 0;
            BrittlenessIndex = 0;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="ed">动态杨氏模量</param>
        /// <param name="muD">动态泊松比</param>
        /// <param name="es">静态杨氏模量</param>
        /// <param name="muS">静态泊松比</param>
        /// <param name="brittlenessIndex">脆性指数</param>
        public RockMechanicsResult(double ed, double muD, double es, double muS, double brittlenessIndex)
        {
            Ed = ed;
            MuD = muD;
            Es = es;
            MuS = muS;
            BrittlenessIndex = brittlenessIndex;
        }

        /// <summary>
        /// 验证计算结果是否合理
        /// </summary>
        /// <returns>是否合理</returns>
        public bool IsValid()
        {
            // 检查杨氏模量范围 (通常在1-100 GPa之间)
            if (Ed < 0 || Ed > 200 || Es < 0 || Es > 200)
                return false;

            // 检查泊松比范围 (通常在0-0.5之间)
            if (MuD < 0 || MuD > 0.5 || MuS < 0 || MuS > 0.5)
                return false;

            // 检查脆性指数范围 (0-100%)
            if (BrittlenessIndex < 0 || BrittlenessIndex > 100)
                return false;

            return true;
        }

        /// <summary>
        /// 获取岩石类型描述
        /// </summary>
        /// <returns>岩石类型</returns>
        public string GetRockTypeDescription()
        {
            if (BrittlenessIndex >= 80)
                return "极脆性岩石";
            else if (BrittlenessIndex >= 60)
                return "高脆性岩石";
            else if (BrittlenessIndex >= 40)
                return "中等脆性岩石";
            else if (BrittlenessIndex >= 20)
                return "低脆性岩石";
            else
                return "韧性岩石";
        }

        /// <summary>
        /// 转换为字符串表示
        /// </summary>
        /// <returns>字符串表示</returns>
        public override string ToString()
        {
            return $"Ed={Ed:F3}GPa, μd={MuD:F4}, Es={Es:F3}GPa, μs={MuS:F4}, BRIT={BrittlenessIndex:F2}%";
        }
    }
}
